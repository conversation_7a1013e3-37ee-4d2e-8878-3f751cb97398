// ignore_for_file: prefer_final_fields, unused_field, prefer_typing_uninitialized_variables, use_key_in_widget_constructors, prefer_interpolation_to_compose_strings, unnecessary_string_interpolations, await_only_futures, avoid_print, prefer_const_constructors, avoid_unnecessary_containers, file_names
import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:magicmate/model/fontfamily_model.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../controller/wallet_controller.dart';

class PayStackWeb extends StatefulWidget {
  final String? email;
  final String? totalAmount;
  final String? initialUrl;
  final FutureOr<NavigationDecision> Function(NavigationRequest request) navigationDelegate;

  const PayStackWeb({this.email, this.totalAmount, this.initialUrl, required this.navigationDelegate});

  @override
  State<PayStackWeb> createState() => _PayStackWebState();
}

class _PayStackWebState extends State<PayStackWeb> {
  GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  late WebViewController _controller;
  var progress;
  String? accessToken;
  String? payerID;
  bool isLoading = true;
  WalletController walletController = Get.put(WalletController());

  @override
  Widget build(BuildContext context) {
    if (_scaffoldKey.currentState == null) {
      return Scaffold(
        body: SafeArea(
          child: Stack(
            children: [
              WebView(
                // initialUrl: "${Config.paymentBaseUrl + "PayStackWeb/index.php?amt=${widget.totalAmount}&email=${widget.email}"}",
                initialUrl: widget.initialUrl,
                javascriptMode: JavascriptMode.unrestricted,
                navigationDelegate: widget.navigationDelegate,
                gestureNavigationEnabled: true,
                onWebViewCreated: (controller) {
                  _controller = controller;
                },
                onPageFinished: (finish) {
                  setState(() async {
                    isLoading = false;
                  });
                },
                onProgress: (val) {
                  setState(() {
                    progress = val;
                  });
                },
              ),
              isLoading
                  ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    SizedBox(
                      child: CircularProgressIndicator(
                        color: gradient.defoultColor,
                      ),
                    ),
                    SizedBox(height: Get.height * 0.02),
                    SizedBox(
                      width: Get.width * 0.80,
                      child: Text(
                        'Please don`t press back until the transaction is complete'
                            .tr,
                        maxLines: 2,
                        textAlign: TextAlign.center,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                            color: Colors.black,
                            fontSize: 15,
                            fontWeight: FontWeight.w500,
                            letterSpacing: 0.5),
                      ),
                    ),
                  ],
                ),
              )
                  : Stack(),
            ],
          ),
        ),
      );
    } else {
      return Scaffold(
        key: _scaffoldKey,
        appBar: AppBar(
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => Get.back(),
          ),
          backgroundColor: Colors.black12,
          elevation: 0.0,
        ),
        body: Center(
          child: Container(
            child: CircularProgressIndicator(
              color: gradient.defoultColor,
            ),
          ),
        ),
      );
    }
  }
}
